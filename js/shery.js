export function sheryJS() {
    // Hero Section Home page
    Shery.imageEffect(".heroimage", {
        style: 6,
        gooey: true,
        config: { "noiseDetail": { "value": 7.63, "range": [0, 100] }, "distortionAmount": { "value": 1.68, "range": [0, 10] }, "scale": { "value": 38.93, "range": [0, 100] }, "speed": { "value": 0.76, "range": [0, 1] }, "zindex": { "value": -9996999, "range": [-9999999, 9999999] }, "aspect": { "value": 2.082236837977518 }, "ignoreShapeAspect": { "value": true }, "shapePosition": { "value": { "x": 0, "y": 0 } }, "shapeScale": { "value": { "x": 0.5, "y": 0.5 } }, "shapeEdgeSoftness": { "value": 0.5, "range": [0, 0.5] }, "shapeRadius": { "value": 0, "range": [0, 2] }, "currentScroll": { "value": 0 }, "scrollLerp": { "value": 0.07 }, "gooey": { "value": true }, "infiniteGooey": { "value": true }, "growSize": { "value": 4, "range": [1, 15] }, "durationOut": { "value": 1, "range": [0.1, 5] }, "durationIn": { "value": 1.5, "range": [0.1, 5] }, "displaceAmount": { "value": 0.5 }, "masker": { "value": true }, "maskVal": { "value": 1, "range": [1, 5] }, "scrollType": { "value": 0 }, "geoVertex": { "range": [1, 64], "value": 1 }, "noEffectGooey": { "value": true }, "onMouse": { "value": 0 }, "noise_speed": { "value": 0.84, "range": [0, 10] }, "metaball": { "value": 0.21, "range": [0, 2] }, "discard_threshold": { "value": 0.58, "range": [0, 1] }, "antialias_threshold": { "value": 0.02, "range": [0, 0.1] }, "noise_height": { "value": 0.29, "range": [0, 2] }, "noise_scale": { "value": 10.69, "range": [0, 100] } }
    });

    // Project Case
    Shery.imageEffect(".projectCase", {
        style: 6,
        gooey: true,
        config: { "noiseDetail": { "value": 6.11, "range": [0, 100] }, "distortionAmount": { "value": 2.14, "range": [0, 10] }, "scale": { "value": 38.93, "range": [0, 100] }, "speed": { "value": 0.76, "range": [0, 1] }, "zindex": { "value": -9996999, "range": [-9999999, 9999999] }, "aspect": { "value": 0.9327999877929688 }, "ignoreShapeAspect": { "value": true }, "shapePosition": { "value": { "x": 0, "y": 0 } }, "shapeScale": { "value": { "x": 0.5, "y": 0.5 } }, "shapeEdgeSoftness": { "value": 0.5, "range": [0, 0.5] }, "shapeRadius": { "value": 0, "range": [0, 2] }, "currentScroll": { "value": 0 }, "scrollLerp": { "value": 0.07 }, "gooey": { "value": true }, "infiniteGooey": { "value": true }, "growSize": { "value": 4, "range": [1, 15] }, "durationOut": { "value": 1, "range": [0.1, 5] }, "durationIn": { "value": 1.5, "range": [0.1, 5] }, "displaceAmount": { "value": 0.5 }, "masker": { "value": true }, "maskVal": { "value": 1.2, "range": [1, 5] }, "scrollType": { "value": 0 }, "geoVertex": { "range": [1, 64], "value": 1 }, "noEffectGooey": { "value": false }, "onMouse": { "value": 0 }, "noise_speed": { "value": 0.84, "range": [0, 10] }, "metaball": { "value": 0.46, "range": [0, 2] }, "discard_threshold": { "value": 0.65, "range": [0, 1] }, "antialias_threshold": { "value": 0.02, "range": [0, 0.1] }, "noise_height": { "value": 0.4, "range": [0, 2] }, "noise_scale": { "value": 11.45, "range": [0, 100] } }
    });

    // Expertise
    Shery.imageEffect(".areasImage", {
        style: 2,
        config: { "resolutionXY": { "value": 100 }, "distortion": { "value": true }, "mode": { "value": -10 }, "mousemove": { "value": 2 }, "modeA": { "value": 0 }, "modeN": { "value": 0 }, "speed": { "value": -2.31, "range": [-500, 500], "rangep": [-10, 10] }, "frequency": { "value": 50, "range": [-800, 800], "rangep": [-50, 50] }, "angle": { "value": 0.72, "range": [0, 3.141592653589793] }, "waveFactor": { "value": 1.4, "range": [-3, 3] }, "color": { "value": 10212607 }, "pixelStrength": { "value": 3, "range": [-20, 100], "rangep": [-20, 20] }, "quality": { "value": 5, "range": [0, 10] }, "contrast": { "value": 1, "range": [-25, 25] }, "brightness": { "value": 1, "range": [-1, 25] }, "colorExposer": { "value": 0.18, "range": [-5, 5] }, "strength": { "value": 0.2, "range": [-40, 40], "rangep": [-5, 5] }, "exposer": { "value": 8, "range": [-100, 100] }, "zindex": { "value": -9996999, "range": [-9999999, 9999999] }, "aspect": { "value": 1.0578135176689285 }, "ignoreShapeAspect": { "value": true }, "shapePosition": { "value": { "x": 0, "y": 0 } }, "shapeScale": { "value": { "x": 0.5, "y": 0.5 } }, "shapeEdgeSoftness": { "value": 0, "range": [0, 0.5] }, "shapeRadius": { "value": 0, "range": [0, 2] }, "currentScroll": { "value": 0 }, "scrollLerp": { "value": 0.07 }, "gooey": { "value": false }, "infiniteGooey": { "value": false }, "growSize": { "value": 4, "range": [1, 15] }, "durationOut": { "value": 1, "range": [0.1, 5] }, "durationIn": { "value": 1.5, "range": [0.1, 5] }, "displaceAmount": { "value": 0.5 }, "masker": { "value": false }, "maskVal": { "value": 1, "range": [1, 5] }, "scrollType": { "value": 0 }, "geoVertex": { "range": [1, 64], "value": 1 }, "noEffectGooey": { "value": true }, "onMouse": { "value": 1 }, "noise_speed": { "value": 0.2, "range": [0, 10] }, "metaball": { "value": 0.2, "range": [0, 2] }, "discard_threshold": { "value": 0.5, "range": [0, 1] }, "antialias_threshold": { "value": 0.002, "range": [0, 0.1] }, "noise_height": { "value": 0.5, "range": [0, 2] }, "noise_scale": { "value": 10, "range": [0, 100] } },
        preset: "./presets/wigglewobble.json",
    });

    // PLayGround Images
    Shery.imageEffect(".playground_images", {
        style: 5,
        scrollSpeed: 12, // Increase the scroll speed for quicker response
        touchSpeed: 12,  // Match touch speed to scroll speed
        damping: 1,
        config: {
            "a": { "value": 0.69, "range": [0, 30] },
            "b": { "value": -0.98, "range": [-1, 1] },
            "zindex": { "value": -9996999, "range": [-9999999, 9999999] },
            "aspect": { "value": 2.0317460317460316 },
            "ignoreShapeAspect": { "value": true },
            "shapePosition": { "value": { "x": 0, "y": 0 } },
            "shapeScale": { "value": { "x": 0.5, "y": 0.5 } },
            "shapeEdgeSoftness": { "value": 0, "range": [0, 0.5] },
            "shapeRadius": { "value": 0, "range": [0, 2] },
            "currentScroll": { "value": 1.997183207363943 },
            "scrollLerp": { "value": 0.2 },  // Increase to reduce delay
            "gooey": { "value": false },
            "infiniteGooey": { "value": true }, // Enable infinite scroll
            "growSize": { "value": 4, "range": [1, 15] },
            "durationOut": { "value": 1, "range": [0.1, 5] },
            "durationIn": { "value": 1.5, "range": [0.1, 5] },
            "displaceAmount": { "value": 0.5 },
            "masker": { "value": false },
            "maskVal": { "value": 1, "range": [1, 5] },
            "scrollType": { "value": 0 },
            "geoVertex": { "range": [1, 64], "value": 1 },
            "noEffectGooey": { "value": true },
            "onMouse": { "value": 0 },
            "noise_speed": { "value": 0.2, "range": [0, 10] },
            "metaball": { "value": 0.2, "range": [0, 2] },
            "discard_threshold": { "value": 0.5, "range": [0, 1] },
            "antialias_threshold": { "value": 0.002, "range": [0, 0.1] },
            "noise_height": { "value": 0.5, "range": [0, 2] },
            "noise_scale": { "value": 10, "range": [0, 100] }
        }
    });

}