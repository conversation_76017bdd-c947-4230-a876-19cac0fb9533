import '../scss/main.scss';
import { createIntroAnimation, init } from './gsap.js';
import { flip_Link_hover } from './flipLink';
import { fetch_Data } from './data/data.js';
import './components/nav/nav.js';
import './components/footer/footer.js';
import "./components/loader/loader.js";
import { sheryJS } from './shery.js';
import lottie from "lottie-web";
import { defineElement } from "@lordicon/element";

// Function to remove the loader and initialize everything else
function removeLoader() {
    // Ensure the body is marked as loaded and the loader is hidden
    document.body.classList.add('loaded');

    // Wait a bit to ensure all elements are in the DOM before initializing anything
    setTimeout(() => {
        fetch_Data();
        flip_Link_hover();
        init();
        defineElement(lottie.loadAnimation);
        createIntroAnimation();
        sheryJS();
    }, 150); // Delay slightly to allow DOM rendering
}

// Add an event listener to remove the loader after the window has fully loaded
window.addEventListener('load', () => {
    // Optional: Add a small delay for a smoother transition
    setTimeout(() => {
        removeLoader();
    }, 500); // Delay to allow the loader to smoothly disappear
});
