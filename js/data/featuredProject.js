// Featured Projects data
const featuredProjects = [
    {
        link: "projectsingle.html",
        title: "ThreadLuxe",
        description: "UIUX Design, Branding",
        images: ["/case1_1.jpg", "/case1_2.jpg"]
    },
    {
        link: "projectsingle.html",
        title: "Ben<PERSON>lear",
        description: "Art Direction, Branding, UIUX Design",
        images: ["/case2_1.jpg", "/case2_2.jpg"]
    },
    {
        link: "projectsingle.html",
        title: "Catalyst",
        description: "UIUX Design, Web Development",
        images: ["/case3_1.png", "/case3_2.png"]
    }
];

// adding featured projects
export function adding_featured_Projects() {
    const featuredProjectsWrapper = document.querySelector('.featured_projects_wrapper');

    if (featuredProjectsWrapper) {
        const groupedProjects = featuredProjects.reduce((acc, project, index) => {
            const rowIndex = Math.floor(index / 2);
            if (!acc[rowIndex]) acc[rowIndex] = [];
            acc[rowIndex].push(project);
            return acc;
        }, []);

        const rows = groupedProjects.map((rowProjects, rowIndex) => `
            <div class="row row${rowIndex + 1}">
                ${rowProjects.map(project => `
                    <a href="${project.link}" class="case">
                        <div class="projectCase">
                            ${project.images.map(image => `<img src="${image}" alt="${project.title}" />`).join('')}
                        </div>
                        <div class="details">
                            <h5 class="font12 splitText">${project.title}</h5>
                            <p class="font12 splitText">${project.description}</p>
                        </div>
                    </a>
                `).join('')}
            </div>
        `).join('');

        featuredProjectsWrapper.innerHTML = rows;
    }
}