//=============== DATA
const accordianData = [
    {
        question: "Branding",
        answer: "Your brand’s identity is its visual voice, and I am here to make sure it speaks volumes. From logo design to comprehensive brand guidelines, I provide cohesive branding solutions that reflect your company's values and resonate with your target audience. My goal is to create a strong, memorable brand identity that sets you apart from the competition.",
        tag: "Brand Guidelines <br> Marketing Collateral <br> Logo Design"
    },
    {
        question: "uiux design",
        answer: "Creating visually stunning and intuitive interfaces is my forte. I specialize in crafting web and mobile app interfaces that not only look beautiful but also provide a seamless user experience. Understanding user needs and behaviors is crucial for designing products that resonate. My UX design services encompass comprehensive user research and analysis, wireframing, and prototyping, as well as thorough usability testing.",
        tag: "Interactive Prototypes <br> Visual Design Systems <br> User Research and Analysis <br> Wireframing and Prototyping <br> Usability Testing"
    },
    {
        question: "motion graphics",
        answer: "Bring your ideas to life with dynamic motion graphics and animations. From explainer videos to animated UI elements and logo animations, I create engaging visual content that can help convey complex ideas simply and effectively. Motion graphics add a layer of sophistication to your digital presence, making your content more compelling and memorable.",
        tag: "Explainer Videos <br> Animated UI Elements <br> Logo Animations",
    },
    {
        question: "Web Development",
        answer: "I build responsive, visually appealing websites that offer a seamless user experience across all devices. By utilizing the latest HTML, CSS, SASS, Tailwind, JS, ReactJS & NextJS technologies, I create interactive features and animations that enhance user engagement. My front-end development services ensure that your website looks great and performs flawlessly. Behind every great website is a powerful backend. I offer server-side scripting, database integration, and API development services to create robust, scalable web applications. My back-end development expertise ensures that your website is fast, secure, and capable of handling your business needs.",
        tag: "Responsive Web Design <br> NextJS Development <br> Interactive Animations <br> Framer Development <br> Server-Side Scripting <br> Database Integration <br> API Development",
    },
    {
        question: "Strategy",
        answer: "Navigate the digital world with confidence through my consultation and strategy services. I offer digital strategy planning, design and development consultation, and user experience audits to help you make informed decisions. By understanding your goals and challenges, I provide actionable insights and tailored strategies to achieve success.",
        tag: "Digital Strategy Planning <br> Design and Development Consultation <br> User Experience Audits",
    },
];

//=============== Function for FAQ
export function fetch_accordionData() {
    //=============== fetchind data for accordian
    function setupAccordian() {
        const services = document.querySelector('.accordians_wrapper');
        if(!services){
            return;
        }
        const serviceHTML = accordianData.map(data => `
            <div class="accordion">
                <div class="question">
                    <h5 class="font36">${data.question}</h5>
                    <div class="close"><img src="close.png"></div>
                </div>
                <div class="answer">
                    <p class="font12">${data.answer}</p>
                    <div class="tags mT20">
                        <p class="font12">${data.tag}</p>
                    </div>
                </div>
            </div>
        `).join('');

        services.innerHTML = serviceHTML;
    }

    //=============== animation for accordian
    function animateAccordian() {
        const accordions = document.querySelectorAll('.accordion');

        function closeAllExcept(current) {
            accordions.forEach((accordion) => {
                const answer = accordion.querySelector('.answer');
                if (answer !== current && answer.style.height !== '0px') {
                    answer.style.height = '0px'; // Close section
                    answer.style.opacity = '0'; // Set opacity to 0
                    accordion.querySelector('.close').classList.remove('rotate');
                }
            });
        }

        accordions.forEach((accordion) => {
            const answer = accordion.querySelector('.answer');
            const close = accordion.querySelector('.close');

            accordion.addEventListener('click', function () {
                const isOpen = answer.style.height !== '0px';

                if (!isOpen) {
                    closeAllExcept(answer); // Close other accordions
                    answer.style.height = `${answer.scrollHeight}px`; // Open this accordion
                    answer.style.opacity = '1'; // Set opacity to 1
                    answer.style.marginTop = '1rem'; // Set margin top
                    close.classList.add('rotate');
                } else {
                    answer.style.height = '0px'; // Close this accordion
                    answer.style.opacity = '0'; // Set opacity to 0
                    answer.style.marginTop = '0'; // Reset margin top
                    close.classList.remove('rotate');
                }
            });

            // Initialize each answer to closed state
            answer.style.height = '0px';
            answer.style.opacity = '0'; // Initialize opacity
            answer.style.marginTop = '0'; // Initialize margin top
        });
    }

    setupAccordian();
    animateAccordian();
}