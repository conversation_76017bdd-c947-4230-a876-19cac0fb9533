const careerData = [
    {
        year: "2024 - Present",
        experiences: [
            { company: "Toptal", role: "Head of Development", image: "aboutrow1.png" },
            { company: "Frog Design", role: "Freelancer as Designer", image: "aboutrow2.png" },
            { company: "IDE<PERSON>", role: "Lead Designer", image: "aboutrow3.png" }
        ]
    },
    {
        year: "2023",
        experiences: [
            { company: "ThoughtBot", role: "Freelancer as a Web Developer", image: "aboutrow4.png" },
            { company: "Balsamiq", role: "UX Researcher", image: "aboutrow5.png" }
        ]
    },
    {
        year: "2022",
        experiences: [
            { company: "UXPin", role: "Head of UX / Director of UX", image: "aboutrow6.png" },
            { company: "Landor", role: "Freelancer as Web Developer", image: "aboutrow7.png" },
            { company: "Hatch Labs", role: "Freelancer as UIUX Designer", image: "aboutrow8.png" }
        ]
    },
    {
        year: "2021",
        experiences: [
            { company: "Cleveroad", role: "Freelancer as Motion Designer", image: "aboutrow9.png" },
            { company: "Y Media Labs", role: "Freelancer as Fullstack Developer", image: "aboutrow10.png" }
        ]
    }
];

export function adding_careerBlocks_about() {
    const careerBlocksElement = document.getElementById("careerBlocks");

    // Check if the element exists
    if (careerBlocksElement) {
        careerBlocksElement.innerHTML = careerData.map(block => `
            <div class="block mT70">
                <div class="heading">
                    <h3 class="font36 splitText">${block.year}</h3>
                </div>
                <div class="wrapper">
                    ${block.experiences.map(exp => `
                        <div class="row" data-image="${exp.image}">
                            <p class="font12 splitText">${exp.company}</p>
                            <p class="font12 splitText">${exp.role}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }
}