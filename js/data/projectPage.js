const projects = [
    {
        name: "Pillars",
        img1: "/case9_1.png",
        img2: "/case9_2.png",
        details: "UIUX Design, Branding Identity",
        link: "projectsingle.html"
    },
    {
        name: "Chi-<PERSON>",
        img1: "/case8_1.jpg",
        img2: "/case8_2.jpg",
        details: "Branding, Brand Identity",
        link: "projectsingle.html"
    },
    {
        name: "<PERSON><PERSON><PERSON>",
        img1: "/case7_1.jpg",
        img2: "/case7_2.jpg",
        details: "Branding, Web Development",
        link: "projectsingle.html"
    },
    {
        name: "Emendor",
        img1: "/case6_1.png",
        img2: "/case6_2.png",
        details: "Web Development",
        link: "projectsingle.html"
    },
    {
        name: "<PERSON><PERSON><PERSON>",
        img1: "/case5_1.png",
        img2: "/case5_2.png",
        details: "UIUX Design, Web Development",
        link: "projectsingle.html"
    },
    {
        name: "Jetpack",
        img1: "/case4_1.jpg",
        img2: "/case4_2.jpg",
        details: "Logo, Branding",
        link: "projectsingle.html"
    },
    {
        name: "ThreadLuxe",
        img1: "/case1_1.jpg",
        img2: "/case1_2.jpg",
        details: "UIUX Design, Branding",
        link: "projectsingle.html"
    },
    {
        name: "Benctlear",
        img1: "/case2_1.jpg",
        img2: "/case2_2.jpg",
        details: "Art Direction, Branding, UIUX Design",
        link: "projectsingle.html"
    },
    {
        name: "Catalyst",
        img1: "/case3_1.png",
        img2: "/case3_2.png",
        details: "UIUX Design, Web Development",
        link: "projectsingle.html"
    }
];

// adding  projects in project page
export function adding_projects_projectPage() {
    const projectsContainer = document.getElementById('projectsContainer');
    if (!projectsContainer) {
        return;
    }

    projectsContainer.innerHTML = projects.map(project => `
        <a href="${project.link}" class="case">
            <div class="projectCase">
                <img src="${project.img1}" alt="${project.name}">
                <img src="${project.img2}" alt="${project.name}">
            </div>
            <div class="details">
                <h5 class="font12 splitText">${project.name}</h5>
                <p class="font12 splitText">${project.details}</p>
            </div>
        </a>
    `).join('');
}