export function flip_Link_hover() {
    const flipLinks = document.querySelectorAll('.flip__link--text');

    flipLinks.forEach(link => {
        const text = link.textContent.trim(); // Get text content directly

        // Clear existing content
        link.innerHTML = '';

        // Split text into characters and create spans
        text.split('').forEach((char, index) => {
            const span = document.createElement('span');
            span.className = 'char';
            span.style.setProperty('--char-index', index);
            span.textContent = char;
            link.appendChild(span);
        });
    });
}