@import "../../../scss/utilities/variables.scss";

footer {
    width: 100%;
    background: #1c1c1c;
    padding: 100px 0 30px 0;

    .container {
        display: flex;
        flex-direction: column;
        gap: 100px;

        /* TOP */
        .top {
            display: flex;
            gap: 50px;

            .left {
                flex: 1;

                h4 {
                    max-width: 600px;
                    width: 100%;
                }

                p {
                    max-width: 380px;
                    width: 100%;
                    margin-top: 10px;
                }
            }

            .right {
                flex: 1;

                p {
                    &:nth-child(1) {
                        color: $grey;
                        margin-bottom: 5px;
                    }
                }
            }
        }

        /* CENTER */
        .center {
            display: flex;
            gap: 50px;

            .left {
                flex: 1;

                p {
                    color: $grey;
                    margin-bottom: 5px;
                }
            }

            .right {
                flex: 1;

                a {
                    margin-bottom: 5px;
                    position: relative;

                    &::before {
                        content: "";
                        width: 5px;
                        height: 5px;
                        background: $white;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: -15px;
                    }
                }
            }
        }

        /* BOTTOM */
        .bottom {
            display: flex;
            gap: 20px;

            p {
                span {
                    color: $grey;
                }
            }
        }
    }

    @media (max-width: 600px) {
        padding: 50px 0 20px 0;

        .container {
            gap: 70px;

            .top {
                flex-direction: column;
                gap: 20px;
            }

            .center {
                flex-direction: column;
                gap: 20px;

                .right {
                    padding-left: 20px;
                }
            }
        }
    }
}