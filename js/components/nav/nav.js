import './nav.scss';

// Define your links
const links = [
    { name: 'Projects', url: 'projects.html' },
    { name: 'About', url: 'about.html' },
    { name: 'Playground', url: 'playground.html' },
    { name: 'Contact', url: 'contact.html' }
];

// Generate the HTML structure
const navHTML = `
<nav>
    <div class="container">
        <a href="/" class="flip__link" id="logo">
            <span class="flip__link--text"><PERSON>&nbsp;Grace</span>
            <span class="flip__link--text"><PERSON>&nbsp;Grace</span>
        </a>
        <div class="nav_links">
            ${links.map(link => `
                <a href="${link.url}" class="flip__link">
                    <span class="flip__link--text">${link.name}</span>
                    <span class="flip__link--text">${link.name}</span>
                </a>
            `).join('')}
        </div>
        <div class="hamburger">
            <div></div>
            <div></div>
        </div>
    </div>
    <div class="navBlur">
        ${'<div></div>'.repeat(4)}
    </div>
</nav>`;

// Insert the nav HTML into the document body
document.body.insertAdjacentHTML('afterbegin', navHTML);

// Create the sliding navigation menu
const slidingNav = document.createElement('div');
slidingNav.className = 'sliding_navigation';
slidingNav.innerHTML = links.map(link => `
    <a href="${link.url}" class="flip__link">
        <span class="flip__link--text">${link.name}</span>
        <span class="flip__link--text">${link.name}</span>
    </a>
`).join('');

// Append the sliding navigation to the document body
document.body.appendChild(slidingNav);

// Function to handle navigation animations and toggling
function animatingNav() {
    const toggleClass = (element, className) => element.classList.toggle(className);
    const hamburger = document.querySelector(".hamburger");
    const navigationRes = document.querySelector(".sliding_navigation");
    const overlay = document.querySelector(".overlay");

    [hamburger, overlay].forEach(element => {
        element.addEventListener('click', () => {
            toggleClass(navigationRes, "active");
            toggleClass(hamburger, "active");
            toggleClass(overlay, "active");
        });
    });
}

// Initialize navigation animations
animatingNav();
