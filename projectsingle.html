<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="favicon.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rebel Grace | Chi - Golf</title>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9SV2DKQ6F7"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-9SV2DKQ6F7');
    </script>
</head>

<body>
    <!--===== display laoder from js component folder nav.js =====-->
    <!--===== display nav from js component folder nav.js =====-->

    <!-- Overlay -->
    <div class="overlay"></div>

    <!--===== Main Content =====-->
    <main id="main">
        <!--===== Header =====-->
        <section class="projectSingle_header">
            <div class="container">
                <div class="projectSingle_heading">
                    <h1 class="font52 splitText">Chi - Golf</h1>
                    <p class="font12 splitText">Branding, Brand identity</p>
                </div>
            </div>
            <div class="projectSingle_header_img mT50">
                <img src="projectSingle1.jpg" alt="About Header">
            </div>
        </section>

        <!--===== Text =====-->
        <section class="projectSingle_txt">
            <div class="container">
                <p class="font12 splitText">
                    Chi-Golf does not need you to shoot a hole-in-one to have a great time! Chi-Golf is a private,
                    member-only community for golf fans that aspires to reimagine leisure time on the course by
                    combining play and exploration, changing the game into a pleasant, engaging pastime. Members eagerly
                    await the opportunity to tee off, relax, and enjoy generous incentives, activities, and privileges
                    that further enhance the game. Every swing is a reason to celebrate and an open source of Vitamin
                    Golf.
                </p>
                <p class="font12 mT20 splitText">
                    Chi-Golf saw the need for a more relaxed and unhurried approach to the game, so the company imbued
                    its brand with a pleasant, fresh ambiance inspired by citrus. Chi-Golf intended to blend the
                    invitingness, invigorating force, and rejuvenation in Chicago into its brand image.
                </p>

                <p class="font12 splitText mT20">Branding Designer: Rakibul Hasan</p>

                <a href="https://dribbble.com/shots/24670018-Chi-Golf-Branding-Brand-Identity" target="_blank"
                    class="flip__link mT50">
                    <span class="flip__link--text">Live&nbsp;Preview</span>
                    <span class="flip__link--text">Live&nbsp;Preview</span>
                </a>
            </div>
        </section>

        <!--===== Images =====-->
        <section class="projectSingle_images">
            <div class="image_holder">
                <img src="projectSingle2.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle3.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle4.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle5.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle6.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle7.jpg" alt="project single image">
            </div>

            <div class="image_holder">
                <img src="projectSingle8.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle9.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle10.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle11.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle12.jpg" alt="project single image">
            </div>
            <div class="image_holder">
                <img src="projectSingle13.jpg" alt="project single image">
            </div>
        </section>
    </main>

    <!--===== Footer =====-->
    <footer></footer>

    <!--===== Scripts =====-->
    <!-- Custom -->
    <script type="module" src="js/main.js" defer></script>
</body>

</html>