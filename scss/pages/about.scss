/* Header */
.about_header {
    padding-top: 100px;
    overflow: hidden;

    .container {
        .about_heading {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;

            h1 {
                max-width: 800px;
                width: 100%;
            }

            lord-icon {
                width: 50px;
                height: 50px;
            }

            @media (max-width: 600px) {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
            }
        }
    }

    .about_header_img {
        width: 100%;
        overflow: hidden;

        img {
            transform: scale(1.5);
        }

        @media (max-width: 600px) {
            height: 50vh;
        }
    }
}

/* Text */
.about_txt {
    .container {
        p {
            max-width: 650px;
            width: 100%;
        }
    }
}

/* Career */
.about_career {
    .container {
        .block {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .heading {
                width: 100%;
            }

            .wrapper {
                max-width: 800px;
                width: 100%;

                .row {
                    border-bottom: 1px solid $borderColor;
                    padding: 20px 0;

                    p {
                        pointer-events: none;

                        &:nth-child(2) {
                            margin-top: 10px;
                            color: $grey;
                        }
                    }
                }
            }
        }
    }
}

/* Mousemove image for all rows */
.image-overlay {
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
    width: 200px;
    height: 200px;
    background-size: cover;
    background-position: center;
    transform: scale(0);
    opacity: 0;
    border: 1px solid $borderColor;

    @media (max-width: 600px) {
        display: none;
    }
}