.about {
    .container {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;

        .txt {
            flex: 1;

            h2 {
                width: 100%;
                max-width: 460px;
            }
        }

        .about_numbers {
            display: flex;
            flex: 1;

            .block {
                flex: 1;

                p {
                    color: $grey;
                }
            }
        }
    }

    @media (max-width: 1030px) {
        .container {
            flex-direction: column;
            align-items: flex-start;

            .about_numbers {
                width: 100%;
            }
        }
    }

    @media (max-width: 600px) {
        .container {

            .about_numbers {
                flex-direction: column;
                gap: 30px;
            }
        }
    }
}
