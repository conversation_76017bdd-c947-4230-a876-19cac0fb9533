.areasWork {
    .container {
        .wrapper {
            display: flex;
            align-items: flex-start;
            gap: 50px;
            overflow: hidden;

            .categories {
                flex: 1;
                display: flex;
                flex-direction: column;

                p {
                    cursor: crosshair;
                    position: relative;
                    padding: 5px;
                    transition: $transition;

                    &::before {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: $white;
                        transition: $transition;
                        transform: scaleX(0);
                        transform-origin: left;
                        z-index: -1;
                    }

                    &:hover {
                        color: $black;
                        font-family: MMMedium;

                        &::before {
                            transform: scaleX(1);
                        }
                    }
                }
            }

            .areasImage {
                flex: 1;
                height: 90vh;
                position: relative;
                width: 100%;
            }

            @media (max-width: 1030px) {
                .areasImage {
                    max-height: 600px;
                }
            }

            @media (max-width: 600px) {
                flex-direction: column-reverse;
                gap: 20px;

                .areasImage {
                    max-height: 600px;
                }

                .categories {
                    width: 100%;
                }
            }
        }
    }
}