.flip__link {
    font-size: 12px;
    letter-spacing: -.05px;
    color: $white;
    display: grid;
    font-family: MMMedium, monospace;
    text-transform: uppercase;

    &:hover {
        .flip__link--text:nth-child(1) {
            opacity: 0;
        }

        .flip__link--text:nth-child(2) {
            opacity: 1;
        }

        .flip__link--text:nth-child(1) .char {
            transform: rotate3d(1, 0.3, 0, -90deg);
        }

        .flip__link--text:nth-child(2) .char {
            transform: rotate3d(0, 0, 0, 90deg);
        }
    }
}

.flip__link--text {
    grid-area: 1/1;
    transition:
        opacity 0.4s cubic-bezier(0.445, 0.05, 0.55, 0.95),
        transform 0.4s cubic-bezier(0.445, 0.05, 0.55, 0.95);

    &:nth-child(2) {
        opacity: 0;
    }

    .char {
        display: inline-block;
        transform-origin: 50% 50% 0.4em;
        transition: transform 0.5s cubic-bezier(0.5, 0, 0, 1);
        transition-delay: calc(0ms + var(--char-index) * 25ms);
        backface-visibility: hidden;
    }

    &:nth-child(2) .char {
        transform: rotate3d(1, -0.5, 0, 90deg);
    }
}