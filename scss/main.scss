@import "utilities/utilities.scss";
@import "pages/pages.scss";

* {
    margin: 0;
    padding: 0;
}

*,
*::after,
*::before {
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    width: 100%;
}

body {
    max-width: 100vw;
    width: 100%;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
    overscroll-behavior: none;
    font-display: swap;
    font-family: MMLight, monospace;
    background: $black;
    color: $white;
    font-feature-settings: normal;
    -webkit-font-smoothing: antialiased;
    font-variation-settings:
        "wght" 400,
        "wdth" 100;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    // opacity: 0;
    // transition: opacity 0.5 ease-in-out;
}

body.loaded {
    opacity: 1;
}

::-webkit-scrollbar {
    height: 10px;
    background: transparent;
    width: 8px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

::-webkit-scrollbar-track {
    background: #000000;
    height: 10px;
    width: 8px;
}
